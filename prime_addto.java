import java.util.*;
public class prime_addto {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter the no. of elements:");
        int n=sc.nextInt();
        int a[]=new int[n];
        for(int i=0;i<n;i++){
           a[i]=sc.nextInt();
        }
        System.out.println("elements are:");
        for(int i=0;i<n;i++){
            System.out.print(a[i]+" ");
        }
        int k=sc.nextInt(),p=2,c=0;
        while(true){
            int f=0;
            for(int i=2;i<p;i++){
                if(p%i==0){
                    f=1;
                    break;
                }
            }
            if(f==0){
                c++;
            }
            if(c==k){
                break;
            }
            p++;
        }
        System.out.println(p);
        for(int i=0;i<n;System.out.print((a[i++]+p)+" "));
    }
}
