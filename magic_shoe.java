import java.util.*;
public class magic_shoe {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter no. of test cases:");
        int d=sc.nextInt();
        int a=sc.nextInt();
        int b=sc.nextInt();
        int n=sc.nextInt();
        int s=sc.nextInt();
        int x=0;
           for(int i=1;i>d;i++){
              a=a*n;
              x=a+b;
           }
           System.out.println((x>=s)? "yes":"no");
    }
}
