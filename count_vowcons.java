import java.util.*;
public class count_vowcons {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter a string:");
        String s=sc.nextLine();
        int i=0,c=0,v=0,sp=0;  // i and sp is for Switch case
        /*for(int i=0;i<s.length();i++){
            if(s.charAt(i)=='a'||s.charAt(i)=='e'||s.charAt(i)=='i'||s.charAt(i)=='o'||s.charAt(i)=='u'||s.charAt(i)=='A'||s.charAt(i)=='E'||s.charAt(i)=='I'||s.charAt(i)=='O'||s.charAt(i)=='U')
            {
              v++;
            }
            else if(s.charAt(i)!=' ')
            {
              c++;
            }
        }
        System.out.println("vowel:"+v);
        System.out.println("consonant:"+c);*/
        switch(s.charAt(i)){
            case 'A','E','I','O','U','a','e','i','o','u':
                v++;
                break;
            case ' ':
                sp++;
                break;
            default:
                c++;
                break;
        }
        System.out.println(v);
        System.out.println(sp);
        System.out.println(c);
    }
}
