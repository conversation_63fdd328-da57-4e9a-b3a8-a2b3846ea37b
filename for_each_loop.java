import java.util.*;
public class for_each_loop {
    public static void main(String[] args) {
        @SuppressWarnings("resource")
        Scanner sc=new Scanner (System.in);
        System.out.println("Array size:");
        int n=sc.nextInt();
        //one-dimension array
        /*int a[]=new int[n];*/
        /*for(int i=0;i<n;i++){
           a[i]=sc.nextInt();
        }*/
        /*for(int i=0;i<n;a[i++]=sc.nextInt());*/
        /*for(int k:a){
            System.out.println(k+" ");
        }*/                                 
        
        // two-dimension array
        int a[][]=new int[n][n];
        for(int i=0;i<n;i++){
            for (int j=0;j<n;j++){
                a[i][j]=sc.nextInt();
            }
        }
        for(int i=0;i<n;i++){
            for (int j=0;j<n;j++){
                System.out.print(a[i][j]+" ");
            }
            System.out.println(); //new line
        }
        for(int k[]:a){
            for(int b:k){            
                System.out.println(b);   //b is a variable name
            }
        }
    }
}
