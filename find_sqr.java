import java.util.Scanner;

public class find_sqr {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println(("No. of elements:")); 
        int n=sc.nextInt();
        int[] a=new int[n];
        System.out.println("numbers:");
        for (int i=0;i<n;i++){
            a[i]=sc.nextInt();
        }
        System.out.println("perfect squares are:");
        for (int i=0;i<n;i++){
            int sqr=(int)Math.sqrt(a[i]);
            if (sqr*sqr==a[i]){
                System.out.println(a[i]);
            }
        }
    }
}
