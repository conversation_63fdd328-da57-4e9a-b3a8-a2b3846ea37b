import java.util.*;
public class palindrome_string {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter a string:");
        String s=sc.nextLine();
        int n=s.length();
        int f=0;
        for(int i=0;i<n/2;i++){
            if(s.charAt(i)!= s.charAt(n-i-1)){
                f=1;
            }
        }
        System.out.println((f==0)? "palindrome":"not a palindrome");
    }
}
