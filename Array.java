import java.util.*;
/**
 * Array
 */
public class Array {

    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner obj=new Scanner(System.in);
        int n=obj.nextInt(), a[][]=new int[n][n],d1=0,d2=0;
        for(int i=0;i<n;i++)
        {
            for(int j=0;j<n;j++)
            {
                a[i][j]=obj.nextInt();
                if(i+j==n-1)
                d1=d1+a[i][j];
                if(i == j)
                d2=d2+a[i][j];
            }
        }
        int d=d1-d2,f=0;
        d=(d<0)?d*-1:d;
        if(d==0|| d==1)
        {
            System.out.println(d+" is neither prime nor not prime");
        }
        else{
            for(int i=2;i<=d/2;i++)
            {
                if(d%i==0)
                {
                    f=1;
                    break;
                }
            }
            System.out.println((f==1)?" not prime":"Prime"); 

        }

         
   
    }
}
