import java.util.Scanner;
public class GroceryBillCalculator {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        double total=0;
        double taxRate=0.05;

        System.out.print("Enter the number of items: ");
        int n = sc.nextInt();

        for(int i = 1; i <= n; i++) {
            System.out.print("Enter item name:");
            String item = sc.next();

            System.out.println("Enter price of" + item + ": " );
            double price=sc.nextDouble();
        
            System.out.println("Enter quantity of" + item +": ");
            int qty=sc.nextInt();

            double cost = price * qty;
            total += cost;  
        }
        
        double tax = total * taxRate;
        double grandTotal = total + tax;

        System.out.println("Total price: " + total);
        System.out.println("Tax: " + tax);
        System.out.println("Grand total: " + grandTotal);
    }
}
