import java.util.*;
public class reverse_string {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter a string:");
        String str=sc.nextLine();
        String rst="";
        for(int i=str.length()-1;i>=0;i--){
            rst=rst+str.charAt(i);
        }
        System.out.println("original string:"+str);
        System.out.println("reversed string:"+rst);
    }
}
