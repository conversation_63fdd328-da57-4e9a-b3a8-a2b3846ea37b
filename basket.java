import java.util.*;
public class basket {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter the elements:");
        int n=sc.nextInt();
        int a[]=new int[n];
        /*int d=0,x,y;*/
        for(int i=0;i<n;i++){
            a[i]=sc.nextInt();
        }
        /*x=a[1]-a[0];
        y=a[2]-a[1];
        if(x<y){
            d=x;
        }
        else{
            d=y;
        }*/
        int d=Math.min(a[1]-a[0],a[2]-a[1]);//
        for (int i=0;i<n;i++){
            if (a[i]+d!=a[i+1]){
                System.out.println(a[i]+d);
                break;
            }
        }
    }
}
