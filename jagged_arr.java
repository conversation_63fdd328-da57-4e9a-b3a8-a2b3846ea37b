import java.util.*;

public class jagged_arr {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);

        // Declare and initialize jagged array
        int a[][] = new int[3][];
        a[0] = new int[2];
        a[1] = new int[5];
        a[2] = new int[4];

        // Input values for the jagged array
        System.out.println("Enter elements for the jagged array:");
        for (int i = 0; i < a.length; i++) {
            for (int j = 0; j < a[i].length; j++) {
                System.out.print("Element at [" + i + "][" + j + "]: ");
                a[i][j] = sc.nextInt();
            }
        }

        // Display the jagged array
        System.out.println("\nThe jagged array is:");
        for (int i = 0; i < a.length; i++) {
            for (int j = 0; j < a[i].length; j++) {
                System.out.print(a[i][j] + " ");
            }
            System.out.println(); // Move to next row
        }

        sc.close();
    }
}
