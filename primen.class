// Source code is decompiled from a .class file using FernFlower decompiler.
import java.util.Scanner;

public class primen {
   public primen() {
   }

   public static void main(String[] var0) {
      Scanner var1 = new Scanner(System.in);
      int var2 = var1.nextInt();
      boolean var3 = false;

      for(int var4 = 2; var4 < var2; ++var4) {
         if (var2 % var4 == 0) {
            System.out.println("not prime");
         } else if (var2 % var4 != 0) {
            System.out.println("prime");
         }
      }

   }
}
