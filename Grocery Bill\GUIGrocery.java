import javax.swing.*;
import java.awt.event.*;

public class GUIGrocery {
    public static void main(String [] args){
        JFrame frame =  new JFrame("Grocery Bill Calculator");
        frame.setSize(400,300);
        
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setLayout(null);
        
        JLabel priceLabel=new JLabel("Price per Item:");
        priceLabel.setBounds(50,30,150,20);
        JTextField priceField=new JTextField();
        priceField.setBounds(180,30,150,20);

        JLabel quantityLabel=new JLabel("Quantity:");
        quantityLabel.setBounds(50,70,150,20);
        JTextField quantityField=new JTextField();
        quantityField.setBounds(180,70,150,20);

        JLabel discountLabel=new JLabel("Discount(%):");
        discountLabel.setBounds(50,110,150,20);
        JTextField discountField=new JTextField();
        discountField.setBounds(180,110,150,20);

        JButton calculateButton=new JButton("Calculate");
        calculateButton.setBounds(100,150,100,30);

        JLabel resultLabel=new JLabel("Total Price: Rs.0.00");
        resultLabel.setBounds(140,200,200,20);
        
        frame.add(priceLabel);
        frame.add(priceField);
        frame.add(quantityLabel);
        frame.add(quantityField);
        frame.add(discountLabel);
        frame.add(discountField);
        frame.add(calculateButton);
        frame.add(resultLabel);

        calculateButton.addActionListener(new ActionListener()
        {
            public void actionPerformed(ActionEvent e)
            {
                try {
                    double price=Double.parseDouble(priceField.getText());
                    int quantity=Integer.parseInt(quantityField.getText());
                    double discount=Double.parseDouble(discountField.getText());

                    double total=price*quantity;
                    double discountAmount=(total*discount)/100;
                    double finalAmount=total-discountAmount;

                    resultLabel.setText("Total Price: Rs."+ String.format("%.2f",finalAmount));
                }
                catch (NumberFormatException ex) {
                    JOptionPane.showMessageDialog(frame, "Invalid input. Please enter valid numbers!");
                }
            }
        });

        frame.setVisible(true);
    }
}
