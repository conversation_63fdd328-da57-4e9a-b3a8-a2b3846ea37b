import java.util.*;
public class palindrome {
    public static void main(String[] args) {
        @SuppressWarnings("resource")//just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter a number:");
        int n=sc.nextInt();
        int r,sum=0,temp;
        temp=n;
        while(n>0){
            r=n%10;
            sum=(sum*10)+r;
            n/=10;
        }
        System.out.println((temp==sum)? "yes":"no");
    }    
}
