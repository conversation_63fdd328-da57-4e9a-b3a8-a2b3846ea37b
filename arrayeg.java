import java.util.*;
public class arrayeg {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println(("No. of elements:"));
        int n=sc.nextInt();
        int a[]=new int[n];
        /*for(int i=0;i<n;i++){
            a[i]=sc.nextInt();
        }*/
        for(int i=0;i<n;a[i++]=sc.nextInt());//
        System.out.println("elements are:");
        /*for(int i=0;i<n;i++){
            System.out.print(a[i]);
        }*/
        for(int i=0;i<n;System.out.print(a[i++]+" "));//
    }
}
