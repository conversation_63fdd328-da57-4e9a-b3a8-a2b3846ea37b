import java.util.*;
public class TwoD_arraysort {
    public static void main(String[]args){
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("no. of elements");
        int n=sc.nextInt();
        int a[]=new int[n];
        int temp;
        System.out.println("enter array elements:");
        for(int i=0;i<n;i++){
            a[i]=sc.nextInt();
        }
        for(int i=0;i<n;i++){
            for(int j=i+1;j<n;j++){
                if(a[i]>a[j]){
                    temp=a[i];
                    a[i]=a[j];
                    a[j]=temp;
                }
            }
            System.out.println("array elements in ascending order:");
            for( i=0;i<n-1;i++){
                System.out.print(a[i]+",");
            } 
        }
        System.out.print(a[n-1]); //error in program
    }
}
