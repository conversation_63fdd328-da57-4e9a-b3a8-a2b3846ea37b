import java.util.*;
public class toggle {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter a string:");
        String s=sc.nextLine();
        char str[]=s.toCharArray(); // convert string array to character array
        for(int i=0;i<str.length;i++){
            str[i]=((char)(str[i]^32));
        }
        for(int i=0;i<str.length;i++){
            System.out.print(str[i]);
        }
    }
}
