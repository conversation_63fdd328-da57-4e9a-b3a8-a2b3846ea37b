import java.util.*;
public class anagram_code {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("s1: ");
        String s1=sc.nextLine();
        System.out.println("s2: ");
        String s2=sc.nextLine();
        int a[]=new int[26];
        int b[]=new int[26];
        int f=0;
        for(int i=0;i<s1.length();i++){
            for (int j=0;j<s2.length();j++){
                a[s1.charAt(i)-'a']++;
                b[s2.charAt(j)-'a']++;
            }
        }
        for(int i=0;i<a.length;i++){
            for(int j=0;j<b.length;j++){
                if(a[i]-b[j] == 0){
                    f=1;
                }
            }
        }
        System.out.println((f==0)? "anagram":"not a anagram");
    }
}
