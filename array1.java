import java.util.*;
public class array1 {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter no. of elements: ");
        int n=sc.nextInt();
        int a[]=new int[n],min=0;
        System.out.println("elements are:");
        for(int i=0;i<n;i++){
            a[i]=sc.nextInt();
            if (n==0){
                 min=a[0];
            }
            else{
                if(min>a[i]){
                    min=a[i];
                }
            }
       }
        /*while (n>0){
            min=a[0];
            for (int i=0;i<n;i++){
                if(min>a[i]){
                    min=a[i];
                }
        }}*/
            System.out.println("minimum value is :"+min);
    }
}
