import java.util.ArrayList;
import java.util.List;

public class Cart {
    private final List<Product> items;

    public Cart() {
        items = new ArrayList<>();
    }

    public void addProduct(Product product) {
        items.add(product);
    }

    public void removeProduct(Product product) {
        items.remove(product);
    }

    public List <Product> getItems() {
        return items;
    }

    public double getTotal() {
        double total = 0;
        for (Product p : items) {
            total+= p.getPrice();
        }
        return total;
    }

    public void clearCart(){
        items.clear();
    }
}
