import java.util.*;
public class armstrong {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter a number:");
        int n=sc.nextInt();
        int d,d1,nd=0;
        d=n;
        d1=n;
        /*while (n>0){
            n/=10;
            nd++;
        }*/
        nd=(int) Math.log10(n)+1;//
        int sum=0;//
        /*while (d>0){
            int r=1;
            int b=d%10;
            for (int i=1;i<=nd;i++){
                r=r*b;
            }
            sum=sum+r;
            d/=10;
        }*/
        while (d>0){
            int b=d%10;
            sum=sum+(int)Math.pow(b,nd);
            d=d/10;
        }//
        System.out.println((d1==sum)? "yes":"no");
    }
}
