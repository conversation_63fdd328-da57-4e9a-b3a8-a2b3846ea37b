import java.util.*;
public class TD_arraysort {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        int n=sc.nextInt();
        int a[][]=new int[n][n];
        for(int i=0;i<n;i++){
            for(int j=0;j<n;j++){
                a[i][j]=sc.nextInt();
            }
        }
        for(int r[]:a)
        {
            Arrays.sort(r);
            for(int i=n-1;i>=0;i--){ //n-1=2 which is index of row
                System.out.print(r[i]+" ");
            }
            System.out.println();
        }
    }
}
