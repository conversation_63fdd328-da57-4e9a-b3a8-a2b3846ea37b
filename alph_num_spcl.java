import java.util.*;
public class alph_num_spcl {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter a string:");
        String s=sc.nextLine();
        int a=0,n=0,sp=0;
        for(int i=0;i<s.length();i++){
            if((s.charAt(i)>='a')&&(s.charAt(i)<='z') || (s.charAt(i)>='A')&&(s.charAt(i)<='Z')){
                a++;
            }
            else if(s.charAt(i)>='0' && s.charAt(i)<='9'){
                n++;
            }
            else{
                sp++;
            }
        }
        System.out.println(a+" "+n+" "+sp);
    }   
}
