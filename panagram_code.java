import java.util.*;
public class panagram_code {
    public static void main(String[] args) {
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter a string:");
        String s=sc.nextLine();
        int a[]=new int[26];
        s=s.toLowerCase();
        System.out.println(s);
        for(int i=0;i<s.length();i++){
            a[s.charAt(i)-'a']++;
        }
        int f=0;
        for(int i=0;i<a.length;i++){
            if(a[i] == 0){
                f=1;
            }
        }
        System.out.println((f==0)? "panagram":"not a panagram");
    }
}
