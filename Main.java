class LinkedList
{
    Node head;
    LinkedList()
    {
        head = null;
    }
    class Node
    {
        int data;
        Node next;
        Node(int data)
        {
            this.data = data;
            next = null;
        }
    }
    public void ial(int d)
    {
        Node newnode = new Node(d);
        if(head == null)
        {
            head = newnode;
        }
        else 
        {
            Node temp = head;
            while(temp.next!=null)
            {
                temp = temp.next;
            }
            temp.next = newnode; 
        }
    }
    public void iaf(int d)
    {
        Node newnode = new Node(d);
        if(head != null)
        {
            newnode.next = head;
        }
        head = newnode;
    }
    public void middle_element()
    {
        if(head == null)
        {
            System.out.println("Hey genius the list is MT");
        }
        else 
        {
            int c = 0;
            Node temp = head;
            for( ;temp != null;c++, temp = temp.next);
            c /= 2; // c = c / 2
            temp = head;
            for( ;c>0;c--, temp = temp.next);
            System.out.println("Middle element" + temp.data);
        }
    }
    public void middle_element_optimal()
    {
        Node r = head;
        Node t = head;
        for( ;r.next!=null && r.next.next!=null ;r = r.next.next, t = t.next);
        System.out.println("middle_element_optimal: "+ t.data);
    }
    public void kth_node_from_last_optimal(int k)
    {
        Node t = head;
        Node r = head;
        for( ;k>0 ;r = r.next, k--);
        for( ;r!=null ; r = r.next, t = t.next);
        System.out.println("kth_node_from_last_optimal: " + t.data) ;
    }
        
    public void display()
    {
        if(head == null)
        {
            System.out.println("Hey genius the list is MT");
        }
        else 
        {
            Node temp = head;
            while(temp != null)
            {  
                System.out.print(temp.data + " ");
                temp = temp.next;
            }
        }
    }
    public void kth_node_from_last(int k)
    {
        if(head == null)
        {
            System.out.println("Hey genius the list is MT");
        }
        else 
        {
            int c = 0;
            Node temp = head;
            for( ;temp != null;c++, temp = temp.next);
            c = c - k;
            temp = head; // important
            for( ;c>0;c--, temp = temp.next);
            System.out.println("kth element" + temp.data);
        }
    }
}
public class Main
{
	public static void main(String[] args) 
	{
		LinkedList l = new LinkedList();
		int d = 6;
		l.iaf(d);
		l.iaf(100);
		l.ial(1);
		l.ial(76);
		l.ial(10);
		l.ial(11);
		l.iaf(500);
		l.middle_element();
		l.middle_element_optimal();
		l.kth_node_from_last(4);
		l.kth_node_from_last_optimal(3);
		l.display();
	}
}