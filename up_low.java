import java.util.*;
public class up_low {
    public static void main(String[] args) {
        int i;
        @SuppressWarnings("resource") // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter a string:");
        String st=sc.nextLine();
        char str[]=st.toCharArray(); // convert string array to character array
        for(i=0;i<str.length;i++){
            if(str[i]>='A' && str[i]<='Z'){
                str[i]=((char)(str[i]+32));
            }
        }
        System.out.println("lowercase:");
        for(i=0;i<str.length;i++){
            System.out.print(str[i]);
        }
    }
}
