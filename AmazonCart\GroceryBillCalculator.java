import java.util.*;

public class GroceryBillCalculator {

    static class Product {
        String name;
        double pricePerUnit;
        int quantity;

        public Product(String name, double pricePerUnit, int quantity) {
            this.name = name;
            this.pricePerUnit = pricePerUnit;
            this.quantity = quantity;
        }

        public double getTotal() {
            return pricePerUnit * quantity;
        }

        public String toString() {
            return name + " x " + quantity + " = ₹" + String.format("%.2f", getTotal());
        }
    }

    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        Stack<String> stockStack = new Stack<>();
        List<Product> cart = new ArrayList<>();

        //ms into stock
        System.out.print("Enter number of items to add to stock: ");
        int stockCount = sc.nextInt();
        sc.nextLine(); // consume newline

        for (int i = 0; i < stockCount; i++) {
            System.out.print("Enter item name: ");
            String name = sc.nextLine();
            System.out.print("Enter price per unit: ");
            double price = sc.nextDouble();
            sc.nextLine(); // consume newline
            stockStack.push(name + "-" + price);
        }

        // Display stock
        System.out.println("\nItems in stock (from top of stack):");
        int index = 0;
        for (String item : stockStack) {
            System.out.println(index + ". " + item);
            index++;
        }

        // Pick items from stack
        System.out.print("\nEnter number of items you want to pick: ");
        int pickCount = sc.nextInt();
        sc.nextLine(); 

        for (int i = 0; i < pickCount; i++) {
            System.out.print("Enter index of item to pick (0 to " + (stockStack.size() - 1) + "): ");
            int pickIndex = sc.nextInt();
            System.out.print("Enter quantity: ");
            int qty = sc.nextInt();
            sc.nextLine(); 

            // Get item from stack by index
            String selectedItem = (new ArrayList<>(stockStack)).get(pickIndex);
            String[] parts = selectedItem.split("-");
            String itemName = parts[0];
            double pricePerUnit = Double.parseDouble(parts[1]);

            cart.add(new Product(itemName, pricePerUnit, qty));
        }

        // Display bill
        System.out.println("\n--- Final Bill ---");
        double total = 0;
        for (Product p : cart) {
            System.out.println(p);
            total += p.getTotal();
        }
        System.out.println("Total Amount: ₹" + String.format("%.2f", total));
    }
}
