import java.util.*;
public class num_system {
    public static void main(String[] args) {
     /* int n=13;
        System.out.printf("%x",n);          // for hexadecimal after 9 the values will be a to f.
        System.out.println();               // n=10 which is a, n=11 which is b, etc.
        System.out.printf("%X",n);          // for %x , then 'a' will be printed.
        System.out.println();               // for %X , then 'A' will be printed.
        System.out.printf("%o",n);  */
        
        // binary to decimal
     /* @SuppressWarnings("resource")  // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter a number:");
        int a=sc.nextInt();
        int s=0,r=0,i=0;
        while(a>0){ 
                r=a%10;
                s=s+r*(int)Math.pow(2,i++); // int - for typecasting 
                a=a/10;
        }
        System.out.println(s);  */

        // octal to decimal
     /* @SuppressWarnings("resource")  // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter a number:");
        int a=sc.nextInt();
        int s=0,r=0,i=0;
        while(a>0){ 
                r=a%10;
                s=s+r*(int)Math.pow(8,i++); // int - for typecasting 
                a=a/10;
        }
        System.out.println(s);  */

        // hexcadecimal to decimal
        @SuppressWarnings("resource")  // just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter a number:");
        String a=sc.next();
        int n=a.length()-1,r=0,s=0;
        for(int i=0;i<n;i++){ 
            if(a.charAt(i)>='A' && a.charAt(i)>='F'){
                r=a.charAt(i)-55;
            }
            else if(a.charAt(i)>='0' && a.charAt(i)>='9'){
                r=a.charAt(i)-'0';
            }
            s=s+r*(int)Math.pow(16,n--);
        }
        System.out.println(s);
    }
}
