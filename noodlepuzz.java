import java.util.*;
public class noodlepuzz {
    public static void main(String[] args) {
        @SuppressWarnings("resource")//just ignore
        Scanner sc=new Scanner(System.in);
        System.out.println("enter the number of rounds:");
        int nr=sc.nextInt();
        /*int cut=0;
        for (int i=1;i*i<=nr;i++){
            cut++;
        }
        System.out.println(cut);    */
        int b=((int)(Math.log10(nr)/Math.log10(2)));
        System.out.println(b);
    }
}
