class Linkedlist
{
    Node head;
    Linkedlist()
    {
        head = null;
    }
    class Node 
    {
        int data;
        Node next;
        Node( int d)
        {
            data = d;
            next = null;
        }
    }
    public void ial(int data)
    {
        Node newnode = new Node(data);
        if(head==null)
        {
            head = newnode;
        }
        else
        {
           Node temp = head;
           for( ; temp.next !=null; temp = temp.next);
           temp.next = newnode;
        }
    }
    public void display()
    {
        Node temp = head;
        if(head==null)
        {
            System.out.println("Hey idiot the list is MT");
        }
        for( ;temp!=null; System.out.print(temp.data + " "), temp = temp.next);
    }
    public void iaf(int d)
    {
        Node newnode = new Node(d);
        if(head != null)
        {
            newnode.next = head;
        }
        
        head = newnode;
        
    }
}
public class Main1
{
	public static void main(String[] args)
	{
	    Linkedlist l = new Linkedlist();
	    l.display();
	    l.ial(1);
	    l.ial(2);
	    l.ial(5);
	    l.ial(10);
	    l.iaf(100);
	    l.display();
		//System.out.println("Hello World");
	}
}
